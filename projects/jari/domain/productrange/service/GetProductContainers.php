<?php

  namespace domain\productrange\service;

  use ArrayHelper;
  use DBConn;
  use DbHelper;
  use domain\productrange\collection\ProductContainers;
  use Pager;
  use ProductCont;
  use ProductContContent;
  use ProductContOption;

  class GetProductContainers {


    /**
     * Get the products (product containers) from the parent category of the product category
     *
     * @param int $parent_category_id
     * @return array
     */
    public function getOnlineOfParentCategory(int $parent_category_id): array {
      $filter_query = "";
      $filter_query .= "JOIN category ON category.online_custshop = 1 ";
      $filter_query .= "AND (category.id = product_cont.category1_id ";
      $filter_query .= "OR category.id = product_cont.category2_id ";
      $filter_query .= "OR category.id = product_cont.category3_id) ";
      $filter_query .= "JOIN category parent_category ON parent_category.id = category.parent_id AND parent_category.online_custshop = 1 ";
      $filter_query .= "JOIN product_cont_content ON product_cont_content.product_cont_id = product_cont.id ";
      $filter_query .= "AND parent_category.id = " . DbHelper::escape($parent_category_id) . " ";
      $filter_query .= "WHERE product_cont.online = 1 ";
      $filter_query .= "ORDER BY product_cont.sort ASC ";

      return $this->fillContainers($filter_query);
    }


    /**
     * Get the products (product containers) from the product category
     *
     * @param int $parentCategoryId
     * @param Pager|null $pager
     * @return array
     * @throws \GsdException
     */
    public function getOnlineOfCategory(int $parentCategoryId, ?Pager $pager, array $filters = []): array {
      $filterQuery = "
        JOIN category ON 
            category.online_custshop = 1 
            AND category.id = " . $parentCategoryId . "
            AND (
                category.id = product_cont.category1_id OR
                category.id = product_cont.category2_id OR
                category.id = product_cont.category3_id
            )
        JOIN product_cont_content ON product_cont_content.product_cont_id = product_cont.id
        WHERE product_cont.online = 1
      ";

      foreach ($filters as $code => $values) {
        if (ArrayHelper::hasData($values)) continue;
        $inClause = DbHelper::getSqlIn('product_cont_option.value', $values);
        $filterQuery .= "
            AND EXISTS (
                SELECT 1 FROM product_cont_option
                WHERE product_cont_option.product_cont_id = product_cont.id
                  AND product_cont_option.code = '$code'
                  AND $inClause
            )
        ";
      }

      $filterQuery .= " ORDER BY product_cont.sort ASC ";

      if ($pager) {
        $countSql = "SELECT COUNT(*) AS count FROM product_cont " . $filterQuery;
        $result = DBConn::db_link()->query($countSql);
        $row = $result->fetch_assoc();
        $pager->count = (int)($row['count'] ?? 0);

        $filterQuery .= $pager->getLimitQuery();
      }

      return $this->fillContainers($filterQuery);
    }

    public function getCategoryList(?array $category_ids): array {
      $product_containers = new ProductContainers();
      $product_containers->selectWithContent();
      $product_containers->joinCategory();
      $product_containers->filterCategory($category_ids);
      $product_containers->joinContent();
      $product_containers->whereNotRemoved();
      $product_containers->orderBy('product_cont.sort', 'ASC');
      return $product_containers->retrieveWithContent();
    }

    public function getAllOnline() {
      $filter_query = "";
      $filter_query .= "JOIN category ON category.id = product_cont.category1_id AND category.online_custshop = 1 ";
      $filter_query .= "JOIN product_cont_content ON product_cont_content.product_cont_id = product_cont.id ";
      $filter_query .= "WHERE product_cont.online = 1 ";
      $filter_query .= "ORDER BY product_cont.sort ASC ";

      return $this->fillContainers($filter_query);
    }

    /**
     * @param string $filterQuery
     * @return array
     * @throws \GsdException
     */
    private function fillContainers(string $filterQuery): array {
      $query = "SELECT product_cont.*, product_cont_content.* FROM product_cont " . $filterQuery;

      $products = [];
      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_row()) {
        $productContainer = new ProductCont();
        $productContainer->hydrate($row);

        $productContainerContent = new ProductContContent();
        $productContainerContent->hydrate($row, count(ProductCont::columns));
        $productContainer->content = $productContainerContent;

        $products[] = $productContainer;
      }

      return $products;
    }

    public function getGroupedOptionsFromProductContainers(array $productContainers): array {
      $ids = array_column($productContainers, 'id');
      $options = ProductContOption::find_all_by(['product_cont_id' => $ids]);

      $grouped = [];
      foreach ($options as $option) {
        $code = $option->code;
        if (!isset($grouped[$code])) {
          $grouped[$code] = (object) [
            'key' => $code,
            'label' => ProductContOption::CODES[$code],
            'values' => [],
          ];
        }
        $grouped[$code]->values[$option->value] = $option->value;
      }
      return array_values($grouped);
    }
  }