<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  product: {
    type: Object,
    default: null
  },
  container: {
    type: Object,
    default: null
  },
  isOpen: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'add-to-basket']);

const dialog = ref(null);

watch(() => props.isOpen, (newValue) => {
  if (newValue && dialog.value) {
    dialog.value.showModal();
  } else if (dialog.value) {
    dialog.value.close();
  }
});

function closeDialog() {
  emit('close');
}

function handleDialogClick(event) {
  if (event.target === dialog.value) {
    closeDialog();
  }
}

function handleKeydown(event) {
  if (event.key === 'Escape') {
    closeDialog();
  }
}

function handleAddToBasket() {
  emit('add-to-basket', props.product);
  closeDialog();
}
</script>

<template>
  <dialog
      ref="dialog"
      class="product-dialog"
      @click="handleDialogClick"
      @keydown="handleKeydown"
  >
    <div class="dialog-content" v-if="container && product">
      <div class="dialog-header">
        <h2>{{ container.content.name }}</h2>
        <button
            type="button"
            class="close-button"
            @click="closeDialog"
            aria-label="Close dialog"
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
            <path d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"/>
          </svg>
        </button>
      </div>

      <div class="dialog-body">
        <div class="info-card">
          <div class="card-header">
            <h3>Productgroep Specificaties</h3>
          </div>
          <div class="spec-grid">
            <div
                v-for="option in container.options"
                :key="option.code"
                class="spec-row"
            >
              <span class="spec-label">{{ option.name }}</span>
              <span class="spec-value">{{ option.value }}</span>
            </div>
          </div>
        </div>

        <div class="info-card">
          <div class="card-header">
            <h3>Product Specificaties</h3>
          </div>

          <div class="spec-grid" v-if="product.code">
            <div class="spec-row">
              <span class="spec-label">Artikelnummer</span>
              <span class="spec-value">{{ product.code }}</span>
            </div>
          </div>

          <div class="spec-grid" v-if="product.options && product.options.length">
            <div
                v-for="option in product.options"
                :key="option.code"
                class="spec-row"
                :class="{ 'highlight': option.name === 'Niet voor' }"
            >
              <span class="spec-label">{{ option.name }}</span>
              <span class="spec-value" :class="{ 'text-warning': option.name === 'Niet voor' }">
                {{ option.value }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <button type="button" class="btn btn-secondary" @click="closeDialog">
          Annuleren
        </button>
        <button type="button" class="btn btn-primary" @click="handleAddToBasket">
          Toevoegen aan winkelwagen
        </button>
      </div>
    </div>
  </dialog>
</template>

<style scoped lang="scss">
.product-dialog {
  border: none;
  border-radius: 12px;
  padding: 0;
  max-width: 600px;
  width: 90vw;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  margin: auto;
  background: white;

  &::backdrop {
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dialog-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border-radius: 12px 12px 0 0;

  h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a1a1a;
  }
}

.close-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f3f4f6;
    color: #374151;
  }
}

.dialog-body {
  padding: 1.5rem 2rem;
  flex-grow: 1;
  overflow-y: auto;
  background: #fafafa;
}

.info-card {
  background: white;
  border-radius: 10px;
  border: 1px solid #e9ecef;
  margin-bottom: 1.5rem;
  overflow: hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.card-header {
  padding: 1rem 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;

  h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.spec-grid {
  padding: 0;
}

.spec-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f8f9fa;
  }

  &.highlight {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
  }
}

.spec-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  flex: 1;
}

.spec-value {
  font-size: 0.875rem;
  color: #1f2937;
  font-weight: 600;
  text-align: right;
  max-width: 60%;
  word-break: break-word;

  &.text-warning {
    color: #d97706;
    font-weight: 700;
  }
}

.dialog-footer {
  padding: 1.5rem 2rem;
  border-top: 1px solid #e9ecef;
  background: white;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  border-radius: 0 0 12px 12px;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 120px;

  &.btn-secondary {
    background-color: #6c757d;
    color: white;
    border: 1px solid #6c757d;

    &:hover {
      background-color: #5a6268;
      border-color: #5a6268;
      transform: translateY(-1px);
    }
  }

  &.btn-primary {
    background-color: var(--color-primary, #0066cc);
    color: white;
    border: 1px solid var(--color-primary, #0066cc);

    &:hover {
      background-color: var(--color-primary-dark, #0052a3);
      border-color: var(--color-primary-dark, #0052a3);
      transform: translateY(-1px);
    }
  }
}

@media (max-width: 768px) {
  .product-dialog {
    width: 95vw;
    max-width: none;
    margin: 1rem auto;
  }

  .dialog-header,
  .dialog-body,
  .dialog-footer {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .spec-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
  }

  .spec-label {
    font-weight: 600;
  }

  .spec-value {
    text-align: left;
    max-width: 100%;
    font-size: 0.9rem;
  }

  .dialog-footer {
    flex-direction: column;
    gap: 0.75rem;

    .btn {
      width: 100%;
      min-width: auto;
    }
  }
}

@media (max-width: 480px) {
  .dialog-header {
    padding: 1rem 1.25rem;

    h2 {
      font-size: 1.25rem;
    }
  }

  .dialog-body {
    padding: 1rem 1.25rem;
  }

  .dialog-footer {
    padding: 1rem 1.25rem;
  }

  .card-header {
    padding: 0.75rem 1.25rem;
  }

  .spec-row {
    padding: 0.75rem 1.25rem;
  }
}
</style>