<?php

  use gsdfw\domain\exactonline\service\ExactOnlineService;
  use gsdfw\domain\exactonline\service\ExactOnlineApi;

  AppModel::loadBaseClass('BaseInvoice');

  class InvoiceModel extends BaseInvoice {

    const INVOICE_STATUS_NEW = 'new';
    const INVOICE_STATUS_INVOICED = 'invoiced';
    const INVOICE_STATUS_REMINDER1 = 'reminder1';
    const INVOICE_STATUS_REMINDER2 = 'reminder2';
    const INVOICE_STATUS_REMINDER3 = 'reminder3';
    const INVOICE_STATUS_PAYED = 'payed';
    const INVOICE_STATUS_DUBIEUZE = 'dubieus';

    const INVOICE_TYPE_INVOICE = 'invoice';
    const INVOICE_TYPE_CREDIT = 'credit';
    const INVOICE_TYPE_REPAIR = 'repair';
    const INVOICE_TYPE_BTWVERLEGD = 'btwverlegd';

    public $datafile = null; //caching of data file
    /**
     * @var InvoiceProduct[]
     */
    public $invoice_products;

    public static $invoicemethods = [
      'incasso'       => "Automatische incasso",
      'overmaken'     => "Overmaken", //overmaken, bezorgen na betalen
      'omnikassa'     => "Rabobank Omnikassa",
      'overmakenpart' => "Overmaken",
      'rembours'      => "Rembours",
      'paypal'        => "PayPal",
      'debitcard'     => "Pinbetaling",
      'cash'          => "Contant",
      'mollie'        => "Mollie",
      'postpay'       => "Achteraf betalen, op rekening",
    ];

    public static $types = [
      self::INVOICE_TYPE_INVOICE    => "Factuur",
      self::INVOICE_TYPE_CREDIT     => "Creditnota",
      self::INVOICE_TYPE_BTWVERLEGD => "BTW verlegd",
      self::INVOICE_TYPE_REPAIR     => "Reparatie",
    ];

    public static function getStati() {
      if (Config::isdefined('INVOICE_STATI')) {
        return Config::get('INVOICE_STATI');
      }

      return [
        Invoice::INVOICE_STATUS_NEW       => "Nieuw",
        Invoice::INVOICE_STATUS_INVOICED  => "Gefactureerd",
        Invoice::INVOICE_STATUS_REMINDER1 => "Herinnering 1",
        Invoice::INVOICE_STATUS_REMINDER2 => "Herinnering 2",
        Invoice::INVOICE_STATUS_REMINDER3 => "Herinnering 3",
        Invoice::INVOICE_STATUS_PAYED     => "Betaald",
        Invoice::INVOICE_STATUS_DUBIEUZE  => "Dubieus",
      ];
    }

    public static function hasStati($status) {
      $stati = Invoice::getStati();

      return isset($stati[$status]);
    }

    /**
     * @deprecated use Invoice::getVattypes()
     * @var int[]
     */
    public static $vats = [
      6  => 6,
      9  => 9,
      21 => 21,
    ];

    /**
     * @return mixed
     */
    public function getRemark() {
      return $this->remark;
    }

    /**
     * @return mixed
     */
    public function getRemarkInternal() {
      return $this->remark_internal;
    }

    /**
     * @param mixed $remark_internal
     */
    public function setRemarkInternal($remark_internal) {
      $this->remark_internal = $remark_internal;
    }

    /**
     * Get possible vattypes
     *
     * @return int[]
     */
    public static function getVattypes() {
      if (Config::isdefined('INVOICE_VATTYPES')) {
        return Config::get('INVOICE_VATTYPES');
      }

      return [
        6  => 6,
        9  => 9,
        21 => 21,
      ];
    }

    /**
     * Get vattypes for use in JS calculations
     *
     * @return array
     */
    public static function getVattypesForJS() {
      $values = [];
      foreach (Invoice::getVattypes() as $vat) {
        $values[$vat] = 0.00;
      }
      $values['vattotal'] = 0.00;

      return $values;
    }

    public function addToVat($vats, $addextra) {
      $defaultvat = $this->determineVat(); //gebruikt altijd de gedefineerde btw
      $defaultvatcalc = $defaultvat / 100;
      $addextra = $defaultvatcalc * $addextra;
      if (!isset($vats[$defaultvat])) {
        $vats[$defaultvat] = $addextra;
      }
      else {
        $vats[$defaultvat] += $addextra;
      }
      return $vats;
    }

    public function getVatSplit($alsoothers = false) {
      if (!isset($this->invoice_products)) {
        $this->invoice_products = InvoiceProduct::getAllByInvoiceId($this->id);
      }
      $vats = [];
      $use_rowvat = Config::isdefined("INVOICE_ROUND_VAT_ROW") && Config::get("INVOICE_ROUND_VAT_ROW") !== false && ($this->invoicedate == "" || $this->getInvoicedate('U') > strtotime(Config::get("INVOICE_ROUND_VAT_ROW")));
      foreach ($this->invoice_products as $prod) {
        if ($prod->vattype == 0 || $prod->type == InvoiceProduct::TYPE_DESCRIPTION || $prod->type == InvoiceProduct::TYPE_SUBTOTAL)
          continue;
        if ($prod->total != 0) {
          if (!isset($vats[$prod->vattype]))
            $vats[$prod->vattype] = 0.00;
          $multi = $prod->vattype / 100;
          $vatval = $prod->total * $multi;
          if ($use_rowvat) {
            $vatval = round($vatval, 2);
          }
          $vats[$prod->vattype] += $vatval;
        }
      }

      if ($alsoothers) { //haal ook andere btw afhankelijke bedragen op, en neem deze mee in de vatsplit

        $defaultvat = $this->determineVat(); //gebruikt altijd de gedefineerde btw
        $defaultvatcalc = $defaultvat / 100;

        $has_xtra_costs = 0;
        if ($this->paymentdiscount != "" && $this->paymentdiscount != null && $this->paymentdiscount != 0) {
          $has_xtra_costs += $defaultvatcalc * $this->paymentdiscount;
        }

        //order costs
        if ($this->handle_costs != "" && $this->handle_costs != null && $this->handle_costs != 0) {
          $has_xtra_costs += $defaultvatcalc * $this->handle_costs;
        }

        //shipping costs
        if ($this->shipping != "" && $this->shipping != 0 && $this->shipping != null) {
          $has_xtra_costs += $defaultvatcalc * $this->shipping;
        }

        if ($this->credits != null && $this->credits != 0) {
          $has_xtra_costs += -$defaultvatcalc * $this->credits;
        }

        if ($has_xtra_costs != 0) { //ik heb extra kosten met BTW!
          if (!isset($vats[$defaultvat])) {
            $vats[$defaultvat] = $has_xtra_costs;
          }
          else {
            $vats[$defaultvat] += $has_xtra_costs;
          }
        }
      }


      return $vats;
    }

    public static function getTypes() {
      if (Config::isdefined('INVOICE_TYPES')) {
        return Config::get('INVOICE_TYPES');
      }

      return Invoice::$types;
    }

    public static function getStatusDesc($key) {
      $stat = Invoice::getStati();
      if (isset($stat[$key])) {
        return $stat[$key];
      }

      return '';
    }

    /**
     * GETTERS AND SETTERS
     */

    /**
     * @return int
     */
    public function getId() {
      return $this->id;
    }

    /**
     * @return mixed
     */
    public function isRepeatInvoice() {
      return $this->is_repeat_invoice;
    }

    /**
     * @param bool $is_repeat_invoice (required)
     */
    public function setIsRepeatInvoice($is_repeat_invoice) {
      $this->is_repeat_invoice = $is_repeat_invoice;
    }

    public function getBetreft($strlen = false) {
      if ($strlen != false && strlen($this->betreft ?? '') > $strlen) {
        return substr($this->betreft, 0, $strlen) . '...';
      }

      return $this->betreft;
    }

    public function getTotal() {
      return $this->total;
    }

    /**
     * @return mixed
     */
    public function getTotalExclProds() {
      return $this->total_excl_prods;
    }


    /**
     * @param $id
     * @return Invoice $invoice
     */
    public static function getInvoiceAndProductsById($id) {
      $invoice = Invoice::find_by_id($id);
      if ($invoice) {
        $invoice->invoice_products = InvoiceProduct::getAllByInvoiceId($invoice->id);
      }

      return $invoice;
    }

    /**
     * Get invoice with products with md5
     *
     * @param $md
     * @return Invoice
     */
    public static function getInvoiceAndProductsByMd5($md) {
      $invoice = Invoice::find_by([], "WHERE MD5(invoice.id)='" . $md . "' ");
      if ($invoice) {
        $invoice->invoice_products = InvoiceProduct::getAllByInvoiceId($invoice->id);
      }

      return $invoice;
    }

    public function getMarge() {
      $totalexcl = $this->total_excl;
      foreach ($this->invoice_products as $pr) {
        $totalexcl -= $pr->size * $pr->piecebuyprice;
      }

      return $totalexcl;
    }

    /**
     * Invoice has discount amount on a row
     * @return bool
     */
    public function hasDiscount(): bool {
      $invoiceproducts = null;
      if (isset($this->invoice_products)) {
        $invoiceproducts = $this->invoice_products;
      }
      else {
        $invoiceproducts = InvoiceProduct::find_all_by(['invoice_id' => $this->id]);
      }
      foreach ($invoiceproducts as $ip) {
        if ($ip->discount > 0) {
          return true;
        }
      }
      return false;
    }

    /**
     * Gets external id
     *
     * @return string
     */
    public function getExternalId() {
      return $this->external_id;
    }

    /**
     * Serialize and sets data (array('owner', 'customer')) to invoice
     *
     * @param unknown_type $data
     */
    public function setData($data) {
      $this->datafile = $data;
      $this->setDirty('data');
    }

    /**
     * Serialize and sets datasto invoice
     *
     * @param User $user
     * @param User $fromuser
     */
    public function setDatas($user, $fromuser) {
      $data = [];
      $data['customer'] = $user;
      $data['owner'] = $fromuser;
      $this->datafile = $data;
      $this->setDirty('data');
    }

    /**
     * Gets unserialized data (array('owner', 'customer')) from invoice
     */
    public function getData() {
      if ($this->datafile == null && $this->id != null) {
        if (file_exists(DIR_UPLOADS . 'invoicedata/' . $this->id . '.dat')) {
          $this->datafile = unserialize(file_get_contents(DIR_UPLOADS . 'invoicedata/' . $this->id . '.dat'));
        }
        else { //.dat bestand bestaat niet? Dan aanmaken. (fallback)
          $data = [];
          $data['customer'] = User::getUserWithOrganById($this->user_id);
          $data['owner'] = User::getUserWithOrganById($this->from_user_id);
          $this->datafile = $data;
          file_put_contents(DIR_UPLOADS . 'invoicedata/' . $this->id . '.dat', serialize($this->datafile));
        }
      }

      return $this->datafile;
    }

    /**
     * Gets unserialized customer data from invoice
     *
     * @return User
     */
    public function getUserData() {
      $data = $this->getData();

      return $data['customer'];
    }

    /**
     * Gets unserialized owner data from invoice
     *
     * @return User
     */
    public function getFromUserData() {
      $data = $this->getData();

      return $data['owner'];
    }

    public function refreshBothUserData() {
      return self::refreshBothUserDataStatic($this);
    }

    public static function refreshBothUserDataStatic(&$invoice) {
      if ($invoice) {
        $data = [];
        $data['customer'] = User::getUserWithOrganById($invoice->user_id);
        $data['owner'] = User::getUserWithOrganById($invoice->from_user_id);
        $invoice->setData($data);
        $invoice->save();

        return true;
      }

      return false;
    }

    public function determineVat() {
      $to_user = $this->getUserData();
      $from_user = $this->getFromUserData();
      $to_country = $to_user->organisation->invoice_country;

      if (Config::get("ORGANISATION_INTRACOMMUNAUTAIR", true) && $to_user->organisation->no_vat == 1) {
        return 0;
      }
      if ($to_user->organisation->invoice_equal == 1) {
        $to_country = $to_user->organisation->country;
      }
      $from_country = $from_user->organisation->invoice_country;
      if ($from_user->organisation->invoice_equal == 1) {
        $from_country = $from_user->organisation->country;
      }
      if ($to_user->organisation->vat_number != "" && $to_country != $from_country) {
        //we hebben vat nummer en niet dezelfde landen
        return 0;
      }
      if ($from_country == 'be') {
        return 21;
      }

      return self::determineVatNew();
    }

    public static function determineVatNew($organ = null, $date = null) {
      $datetime = time();
      if ($date != null) {
        $datetime = strtotime($date);
      }
      if ($datetime >= strtotime('2012-10-01 00:00:00')) {
        return 21;
      }

      return 19;
    }

    public static function determineVatNewCalc($organ = null) {
      $vat = self::determineVatNew($organ);
      $vat = $vat / 100;

      return $vat;
    }

    public static function determineVatNewCalcM($organ = null) {
      $vat = self::determineVatNew($organ);
      $vat = ($vat / 100) + 1;

      return $vat;
    }

    public function getPaymentObject() {
      $classname = 'Payment' . ucfirst((string)$this->paymentmethod);

      return new $classname;
    }

    /**
     * Get insertTS
     * @param string $format
     * @return string
     */
    public function getInsertTS(string $format = 'd-m-Y H:i:s'): string {
      return DateTimeHelper::formatDbDate($this->insertTS, $format);
    }

    public function save(&$errors = []) {
      if ($this->from_db == false && $this->insertTS == null) {
        $this->insertTS = date('Y-m-d H:i:s');
      }
      $this->updateTS = date('Y-m-d H:i:s');

      $putdata = $this->hasDirty('data');

      $result = parent::save($errors);

      if ($putdata) { //serialize data to invoicedata folder
        file_put_contents(DIR_UPLOADS . 'invoicedata/' . $this->id . '.dat', serialize($this->datafile));
      }

      return $result;
    }

    /**
     * Set the invoice to invoiced
     * Export to accounting software if applies
     *
     * @throws Exception  Might throw an exception if Exact Online is used
     */
    public function setInvoiced() {
      if ($this->status == Invoice::INVOICE_STATUS_INVOICED)
        return;

      $to_user = User::getUserWithOrganById($this->user_id);
      $from_user = User::getUserWithOrganById($this->from_user_id);

      $exact_online_service = new ExactOnlineService();
      if ($exact_online_service->isUsed()) {
        // invoice nr is set by Exact Online
      }
      elseif ($this->invoice_nr == "" || $this->invoice_nr == null) {
        $this->invoice_nr = $from_user->organisation->getNewInvoicenumber();
      }

      $this->status = Invoice::INVOICE_STATUS_INVOICED;
      if ($this->invoicedate == "") {
        $this->invoicedate = date('Y-m-d');
      }

      $this->paymentterm = $to_user->organisation->getPaymentterm();
      $this->save();
      InvoiceStatus::setStatus($this);

      if ($exact_online_service->isUsed()) {
        $exact_online_account = $exact_online_service->getAccount($from_user->organisation_id);
        $exact_online_service->validateAccount($exact_online_account);
        $exact_online_service->addOrUpdateInvoice($exact_online_account, $this);
        $exact_online_service->setInvoiceProcessed($exact_online_account, $this);
        $this->save(); // exact online set the invoice number and date, save on object

        if (PROJECT == 'kennisvanijs') {
          $exact = ExactOnline::find_by(array('organisation_id'=>"2"));
          $exact_invoice = ExactOnlineApi::getInvoiceByInvoiceID($exact, $this->external_id);
          $this->invoice_nr = $exact_invoice['data'][0]->InvoiceNumber;
          $this->save();
        }
      }
    }

    /**
     * Set invoice paid
     */
    public function setPaid() {
      if ($this->status == Invoice::INVOICE_STATUS_PAYED) {
        return;
      }

      if ($this->status == Invoice::INVOICE_STATUS_NEW) {
        $this->setInvoiced();
      }

      $this->status = Invoice::INVOICE_STATUS_PAYED;
      if ($this->payeddate == "") {
        $this->payeddate = date('Y-m-d');
      }
      $this->save();
      InvoiceStatus::setStatus($this);
    }

    /**
     * Online afgerekend. Factuur direct op betaald. Overige facturen meteen factureren.
     * @return void
     * @throws GsdDbException
     * @throws GsdException
     */
    public function paid_online() {
      if ($this->status != Invoice::INVOICE_STATUS_PAYED) {
        $to_user = User::getUserWithOrganById($this->user_id);
        try {
          $this->setInvoiced();
          $this->setPaid();
        }
        catch (Exception $exception) {
          // this should never happen
          $errors_msg = sprintf('Factuur van online betaalde bestelling kon niet gefactureerd of betaald worden: %s', $exception->getMessage());
          logToFile('invoice_invoiced', $errors_msg);
          (new GsdExceptionHandler())->sendMail(PROJECT . ' [error]', nl2br($errors_msg));
          return;
        }

        //creditgeschiedenis welke op de factuur staat, moet hier wel worden afgeboekt.
        Credit::useCredits($to_user->organisation, $this);

        //voor marges
        foreach (InvoiceProduct::getAllByInvoiceId($this->id) as $ip) {
          if ($ip->product_id == null) continue;
          $pr = Product::find_by_id($ip->product_id);
          if ($pr) {
            $ip->piecebuyprice = $pr->getSellSize() * $pr->price_buy;
            $ip->save();
          }
        }

        $related_order = false;
        if(!empty($this->order_id)) {
          $order = Orders::getOrderAndInvoices($this->order_id);
          if($order) {
            $related_order = Orders::getOrderAndInvoices($order->related_order_id);
            if ($order->status != "new" && $order->status != "backorder") {
              $order->status = !$order->is_backorder ? 'new' : 'backorder';
              $order->save();
              OrderMessage::saveStatuschange($order->id, $order->status);
            }

            //send order email
            MailsFactory::sendOrderEmail($order, $this->id, $to_user);

          }
        }

        //heeft online betaald, dus krijgt ook direct zijn factuur.
        if (!in_array(PROJECT, ['textielgroep', 'grafikus', 'praktijktala', 'geldropcentrum', 'kisscards', 'desmaak'])) { //niet direct factuur verzenden
          MailsFactory::sendInvoice($this, false);

          /** @var Invoice $invoice */
          foreach ($order->invoices as $invoice) {
            $invoice->invoiced();
          }
        }

        if ($related_order) { //send status backorder
          foreach ($related_order->invoices as $rel_invoice) {
            if ($rel_invoice->user_id == $this->user_id) {
              $rel_invoice->paid_online();
              break;
            }
          }
        }
      }

    }

    /**
     *  Zet factuur op gefactureerd / betaald en optioneel verzend email
     * (Word niet aangroepen door een online betaalde factuur)
     *
     * @param bool $sendemail
     */
    public function invoiced($sendemail = true) {

      if ($this->status != Invoice::INVOICE_STATUS_NEW)
        return;

      $issend = true;
      $luser = User::getUserWithOrganById($this->from_user_id);
      $to_user = User::getUserWithOrganById($this->user_id);

      $order = null;
      if ($this->order_id != "") {
        $order = Orders::find_by_id($this->order_id);
        if ($order->status == Orders::STATUS_PAID) {
          $issend = false;
        }
      }

      $this->useCredits($to_user->organisation->credits);

      $this->setInvoiced(); // this might throw an Exception, but we dont want to catch it, just show the PHP warning

      if (Config::isdefined('ORDER_SEND_INVOICE_STATUS')) {
        if (Config::get('ORDER_SEND_INVOICE_STATUS') == Invoice::INVOICE_STATUS_PAYED) {
          $this->setPaid();
        }
        else {
          //andere status.. komt nog niet voor...
          $this->status = Config::get('ORDER_SEND_INVOICE_STATUS');
          $this->save();
          InvoiceStatus::setStatus($this);
        }
      }

      //voor marges
      foreach (InvoiceProduct::getAllByInvoiceId($this->id) as $ip) {
        if ($ip->product_id == null)
          continue;
        $pr = Product::find_by_id($ip->product_id);
        if ($pr) {
          $ip->piecebuyprice = $pr->getSellSize() * $pr->price_buy;
          $ip->save();
        }
      }


      if ($sendemail) {
        MailsFactory::sendInvoice($this, $issend);
      }

      Credit::useCredits($to_user->organisation, $this);

    }

    public function revertStock() {
      if (!STOCK_ENABLED)
        return;
      $invoice_products = InvoiceProduct::find_all_by(['invoice_id' => $this->id]);
      foreach ($invoice_products as $prod) {
        if ($prod->product_id != "") {
          $product = Product::find_by_id($prod->product_id);
          if ($product) {
            $product->addToStockByProductSize($prod->size);
            $product->setStockChangeOrigin(sprintf('Bestelling/factuur %s verwijderd', $this->invoice_nr ?: 'id: ' . $this->id));
            $product->save();
          }
        }
      }
    }

    /**
     * Remove items in invoice from stock. On creation of order/invoice
     */
    public function removeFromStock() {
      if (!STOCK_ENABLED)
        return;
      foreach ($this->invoice_products as $prod) {
        if ($prod->product_id != null && $prod->product_id != "" && $prod->product_id != "0") {
          $product = Product::find_by_id($prod->product_id);
          if ($product) {
            $product->removeFromStockByProductSize($prod->size);
            // order nr is not yet set here on the orders object, so we are forced to only set the order id
            $product->setStockChangeOrigin(sprintf('Bestelling/factuur %s aangemaakt/gewijzigd', $this->order_id));
            $product->save();
          }
        }
      }
    }

    /**
     * Add items in invoice from stock. This is on edit invoice or removal of order.
     */
    public function addToStock() {
      if (!STOCK_ENABLED)
        return;
      foreach ($this->invoice_products as $prod) {
        if ($prod->product_id != null && $prod->product_id != "" && $prod->product_id != "0") {
          $product = Product::find_by_id($prod->product_id);
          if ($product) {
            $product->addToStockByProductSize($prod->size);
            $product->setStockChangeOrigin(sprintf('Bestelling/factuur %s aangepast/verwijderd', $this->invoice_nr ?: 'id: ' . $this->id));
            $product->save();
          }
        }
      }
    }

    public function getStatusColor() {
      return self::getStatusColorStatic($this->status);
    }

    public static function getStatusColorStatic($statuss) {
      switch ($statuss) {
        case Invoice::INVOICE_STATUS_NEW:
          return 'background-color:#f8f8fb; color:black;';
          break;
        case Invoice::INVOICE_STATUS_INVOICED:
          return 'background-color:yellow; color:black;';
          break;
        case Invoice::INVOICE_STATUS_REMINDER1:
        case Invoice::INVOICE_STATUS_REMINDER2:
        case Invoice::INVOICE_STATUS_REMINDER3:
          return 'background-color:#A1002A; color:white;';
          break;
        case Invoice::INVOICE_STATUS_PAYED:
          return 'background-color:#74A201; color:white;';
          break;
        case Invoice::INVOICE_STATUS_DUBIEUZE:
          return 'background-color:#EAEBEC;';
          break;
      }

      return '';
    }

    /**
     * Set invoice to previous status
     *
     * @return array
     */
    public function toPreviousStatus() {
      if ($this->status != Invoice::INVOICE_STATUS_REMINDER3 && $this->reminder3date != null && $this->reminder3date != "0000-00-00") {
        $this->payeddate = null;
        $this->status = Invoice::INVOICE_STATUS_REMINDER3;

        return ['success' => true, 'message' => "Factuur is op status 3e herinnering gezet."];

      }
      elseif ($this->status != Invoice::INVOICE_STATUS_REMINDER2 && $this->reminder2date != null && $this->reminder2date != "0000-00-00") {
        $this->status = Invoice::INVOICE_STATUS_REMINDER2;
        $this->reminder3date = null;
        $this->payeddate = null;

        return ['success' => true, 'message' => "Factuur is op status 2e herinnering gezet."];
      }
      elseif ($this->status != Invoice::INVOICE_STATUS_REMINDER1 && $this->reminder1date != null && $this->reminder1date != "0000-00-00") {
        $this->status = Invoice::INVOICE_STATUS_REMINDER1;
        $this->reminder2date = null;
        $this->reminder3date = null;
        $this->payeddate = null;

        return ['success' => true, 'message' => "Factuur is op status 1e herinnering gezet."];
      }
      elseif ($this->status == Invoice::INVOICE_STATUS_INVOICED) {
        $this->reminder1date = null;
        $this->reminder2date = null;
        $this->reminder3date = null;
        //$this->invoicedate = null;
        $this->payeddate = null;
        $this->status = Invoice::INVOICE_STATUS_NEW;

        $this->flag_2 = 0; //verzonden
        $this->flag_3 = 0; //intern
        $this->paymentmethod = "";
        $this->paymentmethod_desc = "";
        $this->paymentdiscount = 0;
        $this->calculateTotals();

        return ['success' => true, 'message' => "Factuur is op status nieuw gezet. Let op: het factuurnummer blijft gekoppeld aan deze factuur!"];
      }
      else {
        $this->reminder1date = null;
        $this->reminder2date = null;
        $this->reminder3date = null;
        $this->payeddate = null;

        $this->flag_2 = 0; //verzonden
        $this->flag_3 = 0; //intern

        $this->status = Invoice::INVOICE_STATUS_INVOICED;

        return ['success' => true, 'message' => "Factuur is op status gefactureerd gezet."];
      }
    }

    public function getDeliverydate($format = 'd-m-Y') {
      return DateTimeHelper::formatDbDate($this->deliverydate, $format);
    }

    public function getInvoicedate($format = 'd-m-Y') {
      return DateTimeHelper::formatDbDate($this->invoicedate, $format);
    }

    public function getInvoicedateDays() {
      $tijd = time();
      if ($this->getPayeddate() != "") {
        $tijd = strtotime($this->payeddate);
      }

      return intval(($tijd - strtotime($this->invoicedate)) / 86400);
    }

    public function getPayeddate($format = 'd-m-Y') {
      return DateTimeHelper::formatDbDate($this->payeddate, $format);
    }

    public function getReminder1date($format = 'd-m-Y') {
      return DateTimeHelper::formatDbDate($this->reminder1date, $format);
    }

    public function getReminder2date($format = 'd-m-Y') {
      return DateTimeHelper::formatDbDate($this->reminder2date, $format);
    }

    public function getReminder3date($format = 'd-m-Y') {
      return DateTimeHelper::formatDbDate($this->reminder3date, $format);
    }

    public static function getFilter($order = true) {
      $join = '';
      $join .= "JOIN user ON user.id=invoice.user_id ";
      $join .= "JOIN organisation ON organisation.id=invoice.organisation_id ";
      $join .= "JOIN user as from_user ON invoice.from_user_id = from_user.id ";
      $join .= "JOIN organisation as from_organisation ON from_organisation.id = from_user.organisation_id ";

      $filterquery = '';
      $filterquery .= " WHERE invoice.id>0 AND ivoid = 0 "; //invoice.id>0 is een truk om filesort te voorkomen....
      $filterquery .= "AND invoice.is_repeat_invoice = 0 "; //periodieke facturen niet tonen
      $ssearch = DbHelper::escape($_SESSION['o_search']);
      if (!empty($ssearch)) {
        $filterquery .= " AND (";
        $filterquery .= " invoice.id  = '" . $ssearch . "'";
        $filterquery .= " OR invoice.invoice_nr LIKE '%" . $ssearch . "%'";
        $filterquery .= " OR invoice.betreft LIKE '%" . $ssearch . "%'";
        $filterquery .= " OR invoice.order_id  = '" . $ssearch . "'";
        $filterquery .= " OR user.firstname LIKE '%" . $ssearch . "%'";
        $filterquery .= " OR user.lastname LIKE '%" . $ssearch . "%'";
        $filterquery .= " OR user.email LIKE '%" . $ssearch . "%'";
        $filterquery .= " OR organisation.cust_nr = '" . $ssearch . "'";
        $filterquery .= " OR organisation.address LIKE '%" . $ssearch . "%'";
        $filterquery .= " OR organisation.zip LIKE '%" . $ssearch . "%'";
        $filterquery .= "  OR organisation.city LIKE '%" . $ssearch . "%'";
        $filterquery .= " OR organisation.name LIKE '%" . $ssearch . "%'";
        if (PROJECT == 'vdlcontainer') {
          $filterquery .= " OR orders.external_nr LIKE '%" . $ssearch . "%'";
          $filterquery .= " OR orders.reference LIKE '%" . $ssearch . "%'";
          $filterquery .= " OR orders.order_nr LIKE '%" . $ssearch . "%'";
        }
        //       $filterquery .= " OR organisation.vat_number LIKE '%" . $ssearch."%'";
        //       $filterquery .= " OR organisation.coc_number LIKE '%" . $ssearch."%')";
        $filterquery .= " ) ";
      }
      if ($_SESSION['o_from'] != "") {
        $filterquery .= " AND invoice.from_user_id = " . escapeForDB($_SESSION['o_from']);
      }
      if ($_SESSION['o_to'] != "") {
        $filterquery .= " AND invoice.user_id = " . escapeForDB($_SESSION['o_to']);
      }
      if (isset($_SESSION['o_country']) && $_SESSION['o_country'] != "") {
        $filterquery .= " AND organisation.country = '" . escapeForDB($_SESSION['o_country']) . "' ";
      }
      if ($_SESSION['o_status']) {
        if ($_SESSION['o_status'] == 'NOTPAYED') {
          $not_paid_statussen = [Invoice::INVOICE_STATUS_REMINDER1, Invoice::INVOICE_STATUS_REMINDER2, Invoice::INVOICE_STATUS_REMINDER3, Invoice::INVOICE_STATUS_INVOICED];
          $filterquery .= " AND " . DbHelper::getSqlIn("invoice.status", $not_paid_statussen);
        }
        elseif ($_SESSION['o_status'] == 'ISINVOICED') {
          $filterquery .= " AND invoice.status != '" . Invoice::INVOICE_STATUS_NEW . "' ";
        }
        elseif ($_SESSION['o_status'] == 'TO_INVOICE') {
          $filterquery .= " AND invoice.status = '" . Invoice::INVOICE_STATUS_NEW . "' ";
        }
        else {
          $filterquery .= " AND invoice.status = '" . escapeForDB($_SESSION['o_status']) . "' ";
        }
      }
      if ($_SESSION['o_variant'] != "") {
        if ($_SESSION['o_variant'] == 'NULL') {
          $filterquery .= " AND invoice.variant IS NULL ";
        }
        else {
          $filterquery .= " AND invoice.variant ='" . escapeForDB($_SESSION['o_variant']) . "' ";
        }
      }
      if ($_SESSION['o_year']) {
        $filterquery .= " AND ( (invoice.invoicedate IS NULL AND YEAR(invoice.insertTS) = " . escapeForDB($_SESSION['o_year']) . ") OR YEAR(invoice.invoicedate) = " . escapeForDB($_SESSION['o_year']) . ") ";
      }
      if ($_SESSION['o_invoice_from'] && $_SESSION['o_invoice_to']) {
        $filterquery .= " AND invoice.invoicedate >= '" . escapeForDB(getTSFromStr($_SESSION['o_invoice_from'])) . "' AND invoice.invoicedate <= '" . getTSFromStr(escapeForDB($_SESSION['o_invoice_to'])) . "' ";
      }

      if ($_SESSION['o_type'] != "") {
        $filterquery .= " AND invoice.type = '" . escapeForDB($_SESSION['o_type']) . "' ";
      }

      if (Config::isTrue("INVOICE_FILTER_EXTERNAL_ENABLED")) {
        if ($_SESSION['o_external_id'] === "1") {
          $filterquery .= " AND invoice.external_id IS NOT NULL ";
        }
        elseif ($_SESSION['o_external_id'] === "2") {
          $filterquery .= " AND invoice.external_id IS NULL ";
        }
      }


      //     echo $filterquery;

      //for filing select boxes and filter and loggedin user
      $userfilt = "";
      $tousers = [];
      $fromusers = [];
      if (PROJECT == 'iwan') {
        if (Privilege::hasRight('GLOBAL_ADMIN')) {
          if ($_SESSION['userObject']->usergroup == User::USERGROUP_ADMIN) {

          }
          elseif ($_SESSION['userObject']->usergroup == User::USERGROUP_ADMIN) {
            $userfilt = " AND " . DbHelper::getSqlIn("usergroup", [User::USERGROUP_BEDRIJF, User::USERGROUP_PARTICULIER, User::USERGROUP_ADMIN]);
          }
          else {
            $userfilt = " AND " . DbHelper::getSqlIn("usergroup", [User::USERGROUP_BEDRIJF, User::USERGROUP_PARTICULIER, User::USERGROUP_ADMIN, User::USERGROUP_SUPERADMIN]);
          }
          $fromusers = User::getUsersWithOrgan($userfilt);
          $tousers = $fromusers;
        }
        if ($_SESSION['userObject']->usergroup == User::USERGROUP_BOEKHOUDER) {
          $filterquery .= " AND invoice.status!='new' AND invoice.flag_3 = 1 ";
        }
        if (isset($_SESSION['o_flag'])) {
          if ($_SESSION['o_flag'] == 1) {
            $filterquery .= " AND invoice.flag=1 ";
          }
          elseif ($_SESSION['o_flag'] == "0") {
            $filterquery .= " AND (invoice.flag=0 OR invoice.flag IS NULL) ";
          }
        }
        if (isset($_SESSION['o_flag_2'])) {
          if ($_SESSION['o_flag_2'] == 1) {
            $filterquery .= " AND invoice.flag_2=1 ";
          }
          elseif ($_SESSION['o_flag_2'] == "0") {
            $filterquery .= " AND (invoice.flag_2=0 OR invoice.flag_2 IS NULL) ";
          }
        }
        if (isset($_SESSION['o_flag_3'])) {
          if ($_SESSION['o_flag_3'] == 1) {
            $filterquery .= " AND invoice.flag_3=1 ";
          }
          elseif ($_SESSION['o_flag_3'] == "0") {
            $filterquery .= " AND (invoice.flag_3=0 OR invoice.flag_3 IS NULL) ";
          }
        }
      }
      else {
        if (Privilege::hasRight('GLOBAL_ADMIN')) {
          if ($_SESSION['userObject']->usergroup == User::USERGROUP_ADMIN) {
            $userfilt = " AND " . DbHelper::getSqlIn("usergroup", [User::USERGROUP_BEDRIJF, User::USERGROUP_PARTICULIER, User::USERGROUP_ADMIN]);
          }
          else {
            $userfilt = " AND " . DbHelper::getSqlIn("usergroup", [User::USERGROUP_BEDRIJF, User::USERGROUP_PARTICULIER, User::USERGROUP_ADMIN, User::USERGROUP_SUPERADMIN]);
          }
          $fromusers = User::getUsersWithOrgan($userfilt);
          $tousers = $fromusers;
        }
        else {
          //alleen zijn facturen aan zichzelf
          $filterquery .= " AND invoice.organisation_id=" . $_SESSION['userObject']->organisation->id . " ";
        }
      }

      if (isset($_SESSION['o_payment_method']) && $_SESSION['o_payment_method'] != "") {
        $filterquery .= " AND invoice.paymentmethod LIKE '" . escapeForDB($_SESSION['o_payment_method']) . "' ";
      }

      $orderquery = '';
      if ($order) {
        if ($_SESSION['o_order'] != "") {
          $orderquery .= " ORDER BY " . DbHelper::escape($_SESSION['o_order']) . " DESC";
        }
        else {
          $orderquery .= " ORDER BY invoice.id DESC";
        }
      }

      $result = [];
      $result['joinquery'] = $join;
      $result['filtquery'] = $filterquery;
      $result['orderquery'] = $orderquery;
      $result['fromusers'] = $fromusers;
      $result['tousers'] = $tousers;

      return $result;
    }

    /**
     * Get filter query for reminders
     * @return array
     */
    public static function getFilterQueryReminders() {
      $reminder_dates = Invoice::getReminderDates();
      $join = "JOIN user ON user.id=invoice.user_id ";
      $join .= "JOIN organisation ON organisation.id=invoice.organisation_id ";
      $join .= "JOIN user as from_user ON invoice.from_user_id = from_user.id ";
      $join .= "JOIN organisation as from_organisation ON from_organisation.id = from_user.organisation_id ";

      $filterquery = " WHERE invoice.id>0 AND ivoid = 0 "; //invoice.id>0 is een truk om filesort te voorkomen....
      $filterquery .= "AND invoice.is_repeat_invoice = 0 "; //periodieke facturen niet tonen
      $filterquery .= "AND invoice.status IN ('" . implode("','", [Invoice::INVOICE_STATUS_INVOICED, Invoice::INVOICE_STATUS_REMINDER1, Invoice::INVOICE_STATUS_REMINDER2, Invoice::INVOICE_STATUS_REMINDER3]) . "') "; //periodieke facturen niet tonen
      $filterquery .= "AND ( ";
      $filterquery .= "(invoice.reminder1date IS NULL AND DATEDIFF(NOW(), invoice.invoicedate) > (invoice.paymentterm + " . $reminder_dates[1] . ") ) ";
      $filterquery .= "OR (NOT invoice.reminder1date IS NULL AND invoice.reminder2date IS NULL AND DATEDIFF(NOW(), invoice.reminder1date) > " . $reminder_dates[2] . " ) ";
      $filterquery .= "OR (NOT invoice.reminder2date IS NULL AND invoice.reminder3date IS NULL AND DATEDIFF(NOW(), invoice.reminder2date) > " . $reminder_dates[3] . " ) ";
      $filterquery .= "OR (NOT invoice.reminder3date IS NULL AND DATEDIFF(NOW(), invoice.reminder3date) > " . $reminder_dates[4] . " ) ";
      $filterquery .= ") ";

      return compact("join", "filterquery");
    }

    /**
     * @param int $user_id
     * @param string $from
     * @param string $to
     * @return Invoice[]
     */
    public static function getInvoicesFromPeriodByUserid($user_id, $from, $to) {
      $invoices = [];
      if ($user_id != "" && $from != "" && $to != "") {
        $query = "SELECT * FROM invoice ";
        $query .= "WHERE 1 ";
        $query .= "AND is_repeat_invoice = 0 "; //periodieke facturen niet tonen
        $query .= " AND invoice.user_id = " . escapeForDB($user_id);
        $query .= " AND invoice.invoicedate >= '" . escapeForDB(getTSFromStr($from)) . "' AND invoice.invoicedate <= '" . escapeForDB(getTSFromStr($to)) . "' ";
        $query .= " ORDER BY invoice.invoice_nr ";

        $result = DBConn::db_link()->query($query);
        while ($row = $result->fetch_row()) {
          $invoice = new Invoice();
          $invoice->hydrate($row);
          $invoices[] = $invoice;
        }
      }

      return $invoices;
    }

    /**
     * Get all invoices with status new of a customer
     *
     * @param      $organisation_id
     * @param bool $ignore_invoice_id : invoice id to skip
     * @return Invoice[]
     */
    public static function getNewInvoicesOfOrganisation($organisation_id, $ignore_invoice_id = false) {
      $invoices = [];
      $query = "SELECT * FROM invoice ";
      $query .= "WHERE is_repeat_invoice = 0 "; //periodieke facturen niet tonen
      $query .= " AND invoice.organisation_id = " . escapeForDB($organisation_id);
      $query .= " AND invoice.status = '" . Invoice::INVOICE_STATUS_NEW . "' ";
      $query .= " AND invoice.ivoid = 0 ";
      if ($ignore_invoice_id !== false && $ignore_invoice_id != '') {
        $query .= " AND invoice.id !=" . $ignore_invoice_id . " ";
      }
      $query .= " ORDER BY invoice.invoice_nr ";

      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_row()) {
        $invoice = new Invoice();
        $invoice->hydrate($row);
        $invoices[] = $invoice;
      }

      return $invoices;
    }


    public static function getInvoicesByIds($ids, $filter = '') {
      $invoices = [];
      if (count($ids) > 0) {
        $query = "SELECT * FROM invoice ";
        $query .= "JOIN invoice_product ON invoice_product.invoice_id = invoice.id ";
        $query .= "WHERE ".DbHelper::getSqlIn("invoice.id", $ids, false);
        $query .= $filter;
        $query .= " ORDER BY invoice.id, invoice_product.sort";
        $result = DBConn::db_link()->query($query);
        while ($row = $result->fetch_row()) {
          $invoice = new Invoice();
          $invoice->hydrate($row);
          if (isset($invoices[$invoice->id])) {
            $invoice = $invoices[$invoice->id];
          }
          $invoice_product = new InvoiceProduct();
          $invoice_product->hydrate($row, count(Invoice::columns));
          $invoice->invoice_products[] = $invoice_product;
          $invoices[$invoice->id] = $invoice;
        }
      }

      return $invoices;
    }

    /**
     * Gebruik credits voor deze factuur
     *
     * @param $credits
     * @return float|int|void
     */
    public function useCredits($credits) {
      if (!CREDITS_ENABLED)
        return 0.00;
      if ($credits == null || $credits <= 0) {
        $this->credits = null;

        return 0.00;
      }

      if ($credits >= $this->total_excl) {
        //totaal factuur word door credits betaald
        $this->credits = $this->total_excl;
        $this->vat = 0;
        $this->total_excl = 0;
        $this->total = 0;
        return $credits - $this->total_excl;
      }
      else {
        //gedeelte van factuur door credits betaald
        $this->total_excl -= $credits;
        $this->credits = $credits;

        $vats = $this->getVatSplit();

        if (isset($vats[19]) && $vats[19] > 0) {
          $this->vat -= 0.19 * $credits;
          $this->total -= 1.19 * $credits;
        }
        elseif (isset($vats[21]) && $vats[21] > 0) {
          $this->vat -= 0.21 * $credits;
          $this->total -= 1.21 * $credits;
        }

        $this->vat = round($this->vat, 2);
        $this->total = $this->total_excl + $this->vat;

        return 0.00;
      }
    }

    /**
     * Controleer of product word gebruikt in niet gefactureerde invoice. Zo ja geef melding.
     *
     * @param $product
     * @return bool|mysqli_result|string
     */
    public static function productUsedInOrder($product) {
      $result = false;
      $query = " SELECT orders.* FROM orders  ";
      $query .= "JOIN invoice ON orders.id = invoice.order_id ";
      $query .= "JOIN invoice_product ON invoice_product.invoice_id = invoice.id AND invoice_product.product_id=" . escapeForDB($product->id) . " ";
      $query .= "WHERE invoice.status ='new' ";
      $query .= "AND invoice.ivoid=0 ";
      $query .= "AND is_repeat_invoice = 0 "; //periodieke facturen niet tonen
      $query .= "AND orders.status != 'cancelled' "; //cancelled orders niet tonen

      $orders = [];
      $r = DBConn::db_link()->query($query);
      while ($row = $r->fetch_row()) {
        $order = new Orders();
        $order->hydrate($row);
        $orders[] = $order;
      }

      if (count($orders) > 0) {
        $result = 'LET OP! De productprijs is aangepast en dit product word gebruikt in niet gefactureerde offertes.<br/> Controleer in de volgende offertes het product met productcode ' . $product->code . ':<br/>';
        foreach ($orders as $do) {
          $result .= $do->getOrderNr() . "<br/>";
        }
      }

      return $result;
    }

    /**
     * Corrects invoice status
     *
     * @param Invoice[] $outstandingInvoices
     *
     * @return int $corrected
     */
    public static function correctOutstandingInvoices($outstandingInvoices) {
      $reverted_ids = [];
      $corrected = 0;

      foreach ($outstandingInvoices as $inv) {
        if ($inv->status == Invoice::INVOICE_STATUS_PAYED) {
          $oldstatus = Invoice::getStatusDesc($inv->status);
          $result = $inv->toPreviousStatus();
          if ($result['success']) {
            $inv->save();
            InvoiceStatus::setStatus($inv); //track invoice status history
            $reverted_ids[] = $inv->id;
            $corrected++;
            logToFile("invoice_status", "Invoice " . $inv->id . " set from status: " . $oldstatus . " to status " . Invoice::getStatusDesc($inv->status));
          }
        }
        else {
          //factuur staat al op gefactureerd, herinnert, nieuw
          $reverted_ids[] = $inv->id;
        }
      }

      //set all other invoices to status paid
      $filterquery = "JOIN organisation O ON O.id = invoice.from_organisation_id ";
      $filterquery .= "WHERE invoice.ivoid = 0 AND O.id = " . escapeForDB(ADMIN_DEFAULT_ID) . " ";
      $filterquery .= "AND invoice.is_repeat_invoice = 0 "; //periodieke facturen niet tonen
      $filterquery .= "AND invoice.external_id != '' AND ".DbHelper::getSqlIn("invoice.status", [Invoice::INVOICE_STATUS_PAYED,Invoice::INVOICE_STATUS_DUBIEUZE], true, true);
      if (count($reverted_ids) > 0) {
        $filterquery .= "AND ".DbHelper::getSqlIn("invoice.id", $reverted_ids, false, true);
      }

      $exactonline = ExactOnline::find_by(['organisation_id' => ADMIN_DEFAULT_ID]); //admin account Paul
      foreach (Invoice::find_all($filterquery) as $paid_invoice) {
        $exactInvoiceResult = ExactOnlineApi::performRequest($exactonline, ExactOnlineApi::API_REF_KEY_SALES_INVOICES, "GET", "\$filter=InvoiceID eq guid'" . $paid_invoice->getExternalId() . "'&\$select=Status");
        if ($exactInvoiceResult['success'] && isset($exactInvoiceResult['data']->results[0])) {

          if ($exactInvoiceResult['data']->results[0]->Status != 50) { //Exact Status Processed
            continue; //skip this invoice because it isn't a processed invoice
          }
        }

        $oldstatus = Invoice::getStatusDesc($paid_invoice->status);
        $paid_invoice->setPaid();

        $corrected++;
        logToFile("invoice_status", "Invoice " . $paid_invoice->id . " set from status: " . $oldstatus . " to status " . Invoice::getStatusDesc($paid_invoice->status));
      }

      return $corrected;
    }

    /**
     * Sends invoice to $tos
     *
     * @param array $tos (required)
     * @param string $subject (required)
     * @param string $message (required)
     * @param array|bool $cc (required)
     * @param array|string $attachments (required)
     * @param bool $send_readreceipt (required)
     *
     * @return array
     */
    public static function send($tos, $subject, $message, $cc, $attachments, $send_readreceipt = false, $status = false) {
      if (count($tos) > 0) {
        $gsd_mailer = GsdMailer::build($tos, $subject, $message);

        if (Config::get("INVOICE_EMAIL_FROM", true)) {
          $gsd_mailer->setFrom(Config::get("INVOICE_EMAIL_FROM"));
          $gsd_mailer->setReturnPath(Config::get("INVOICE_EMAIL_FROM"));
        }
        $send_copy_to = [];
        if ($cc !== false) {
          $send_copy_to = $cc;
        }
        if (Config::get("INVOICE_EMAIL_CC", true)) {
          $send_copy_to = array_merge($send_copy_to, Config::get("INVOICE_EMAIL_CC"));
        }
        if (Config::get("INVOICE_EMAIL_CC_NEW_ONLY", true) && $status != false && !in_array($status, [Invoice::INVOICE_STATUS_REMINDER1, Invoice::INVOICE_STATUS_REMINDER2, Invoice::INVOICE_STATUS_REMINDER3])) {
          $send_copy_to = array_merge($send_copy_to, Config::get("INVOICE_EMAIL_CC_NEW_ONLY"));
        }
        if (count($send_copy_to) > 0) {
          $gsd_mailer->setCc($send_copy_to);
        }
        if (Config::get("INVOICE_EMAIL_BCC", true)) {
          $gsd_mailer->setBcc(Config::get("INVOICE_EMAIL_BCC"));
        }
        if (is_array($attachments)) {
          $gsd_mailer->setFiles($attachments);
        }
        elseif (!empty($attachments)) {
          $gsd_mailer->addFile($attachments);
        }
        if ($send_readreceipt && isset($_SESSION['userObject'])) { //kan verzonden worden vanuit jobqueue
          $gsd_mailer->setReadReceiptTo($_SESSION['userObject']->email);
        }
        $gsd_mailer->send();

        logToFile('mails', "sendInvoice: " . $subject . " " . print_r($tos, true));

        return ['success' => true, 'message' => 'Factuur email(s) succesvol verstuurd.'];
      }
      else {
        return ['success' => false, 'errors' => ["Factuur ontvanger heeft geen geldig emailadres!"]];
      }
    }

    /**
     * Generates a PDF-file for the given $invoice
     *
     * @param int $id (required)
     * @param string $variant (optional)
     * @param string $pdfType
     * @return string|false string with filepath to pdf
     */
    public static function generatePDF($id, string $variant = Invoice::INVOICE_STATUS_INVOICED, string $pdfType = "invoice"): string|false {

      $invoicePdf = new InvoicePdf();
      $invoicePdf->addInvoiceid($id);
      $invoicePdf->setShowvariant($variant);
      $invoicePdf->setPdfType($pdfType);
      $invoicePdf->setCheckMayView(false); //geen rechten controleren
      $invoicePdf->setRedirect(false); //niet redirecten
      $invoicePdf->generatePdf();

      if(file_exists($invoicePdf->getFilepath())) {
        return $invoicePdf->getFilepath();
      }
      return false;
    }

    /**
     * @param Invoice $invoice (required)
     * @param int $payment_term (required)
     * @param User $customer (required)
     * @param Orders $order (optional)
     * @param User $currentuser (optional)
     *
     * @return array
     */
    public static function getEmailText($invoice, $payment_term, $customer, $order = null, $currentuser = null) {

      // get local from customer
      $language = $customer->organisation->language;
      Trans::loadMainLanguagefile($language);
      Trans::loadLanguagefiles('invoice', null, $language);

      if (!$currentuser) {
        $currentuser = $_SESSION['userObject'];
      }

      $tostatus = Invoice::getToStatus($invoice, $payment_term);
      $init_subject = "";
      $init_message = "";

      $aanhef = $customer->getAanhef();
      if (Config::isTrue("ORGANISATION_INVOICE_ADDRESS_CONTACT_NAME_ENABLED") && $customer->organisation->invoice_contact_name != "") {
        $aanhef = $customer->organisation->invoice_contact_name;
      }

      if ($tostatus == Invoice::INVOICE_STATUS_REMINDER1 || ($tostatus === false && $invoice->status == Invoice::INVOICE_STATUS_REMINDER1)) {
        $init_subject = __("1e herinnering openstaande factuur") . ": " . $invoice->invoice_nr;
        $init_message = getLanguageFile($language, 'mail_invoice_rem1.html');
      }
      elseif ($tostatus == Invoice::INVOICE_STATUS_REMINDER2 || ($tostatus === false && $invoice->status == Invoice::INVOICE_STATUS_REMINDER2)) {
        $init_subject = __("2e herinnering openstaande factuur") . ": " . $invoice->invoice_nr;
        $init_message = getLanguageFile($language, 'mail_invoice_rem2.html');
      }
      elseif ($tostatus == Invoice::INVOICE_STATUS_REMINDER3 || $invoice->status == Invoice::INVOICE_STATUS_REMINDER3) {
        $init_subject = __("3e herinnering openstaande factuur") . ": " . $invoice->invoice_nr;
        $init_message = getLanguageFile($language, 'mail_invoice_rem3.html');
      }
      // invoice status new is only used for proforma invoices
      elseif ($invoice->status == Invoice::INVOICE_STATUS_NEW || $invoice->status == Invoice::INVOICE_STATUS_INVOICED || ($invoice->status == Invoice::INVOICE_STATUS_PAYED && $invoice->flag_2 == 0)) { //voor iedereen mogelijk ( && Privilege::hasRight('GLOBAL_ADMIN'))
        $init_subject = __("Uw factuur") . ": " . $invoice->invoice_nr;
        if ($invoice->paymentmethod == Payment::PAYMENT_INCASSO) {
          $init_message = getLanguageFile($language, 'mail_invoice_incasso.html');
        }
        elseif ($invoice->paymentmethod == Payment::PAYMENT_CASH || $invoice->paymentmethod == Payment::PAYMENT_DEBIT_CARD) {
          $init_message = getLanguageFile($language, 'mail_invoice_' . $invoice->paymentmethod . '.html');
        }
        else {
          $init_message = getLanguageFile($language, 'mail_invoice_standard.html');
        }
      }
      $init_message .= $currentuser->getEmailFooter('<br/>');

      $keys = User::getKeys($invoice->getUserData(), $invoice);
      $keys['[*AANHEF*]'] = $aanhef;
      $keys['[*INVOICE_NR*]'] = $invoice->invoice_nr;
      $keys['[*INVOICE_DATE*]'] = $invoice->getInvoicedate();
      $keys['[*INVOICE_DUEDATE*]'] = date('d-m-Y', strtotime("+7 DAYS"));
      $keys['[*INVOICE_VERVALDATUM*]'] = date('d-m-Y', strtotime($invoice->invoicedate . "+" . $payment_term . " DAYS"));
      $keys['[*INVOICE_DAYSTOLATE*]'] = intval((time() - strtotime($invoice->invoicedate ?? '')) / 86400) - $payment_term;
      if ($order != null) {
        $keys['[*ORDER_NR*]'] = $order->getOrderNr();
      }

      $init_subject = StringHelper::replaceKeys($init_subject, $keys);
      $init_message = StringHelper::replaceKeys($init_message, $keys);

      //reload working language
      Trans::clearTrans();
      Trans::loadMainLanguagefile();
      Trans::loadLanguagefiles('invoice');

      return [
        'subject' => $init_subject,
        'message' => $init_message,
      ];
    }

    public static function getReminderDates() {
      $reminder_dates = [
        1 => 14,
        2 => 14,
        3 => 14,
        4 => 5,
      ];
      if (Config::get("INVOICE_REMINDER_DATES", true)) {
        $reminder_dates = Config::get("INVOICE_REMINDER_DATES");
      }

      return $reminder_dates;
    }


    public function mayRemind1() {
      $reminder_dates = Invoice::getReminderDates();

      return $this->status == Invoice::INVOICE_STATUS_INVOICED && strtotime($this->invoicedate . ' +' . ($this->paymentterm + $reminder_dates[1]) . ' DAYS') < time();
    }

    public function mayRemind2() {
      $reminder_dates = Invoice::getReminderDates();

      return $this->status == Invoice::INVOICE_STATUS_REMINDER1 && strtotime($this->reminder1date . ' +' . $reminder_dates[2] . ' DAYS') < time();
    }

    public function mayRemind3() {
      $reminder_dates = Invoice::getReminderDates();

      return $this->status == Invoice::INVOICE_STATUS_REMINDER2 && strtotime($this->reminder2date . ' +' . $reminder_dates[3] . ' DAYS') < time();
    }


    /**
     * May view this invoice
     *
     * @param User $user
     * @return bool
     */
    public function mayView($user) {
      if (!isset($user) || $user == null)
        return false;
      if ($user->usergroup == User::USERGROUP_SUPERADMIN || $user->usergroup == User::USERGROUP_ADMIN || $user->usergroup == User::USERGROUP_WERKNEMER) {
        return true;
      }
      if ($this->organisation_id == $user->organisation_id) { //deze factuur is gericht aan de organisation van deze user
        return true;
      }
      if ($this->from_organisation_id == $user->organisation_id) { //deze factuur is verzonden door de organisation van deze user
        return true;
      }

      return false;
    }


    /**
     * @param Invoice $invoice (required)
     * @param int $payment_term (required)
     *
     * @return bool|string
     */
    public static function getToStatus($invoice, $payment_term) {
      $tostatus = false;

      if ($invoice->mayRemind1()) {
        $tostatus = Invoice::INVOICE_STATUS_REMINDER1;
      }
      elseif ($invoice->mayRemind2()) {
        $tostatus = Invoice::INVOICE_STATUS_REMINDER2;
      }
      elseif ($invoice->mayRemind3() || $invoice->status == Invoice::INVOICE_STATUS_REMINDER3) {
        $tostatus = Invoice::INVOICE_STATUS_REMINDER3;
      }
      elseif ($invoice->status == Invoice::INVOICE_STATUS_PAYED && $invoice->flag_2 == 0) { //factuur reeds betaald via pin/contant, factuur moet nog verzonden worden.
        $tostatus = Invoice::INVOICE_STATUS_PAYED;
      }
      elseif ($invoice->status == Invoice::INVOICE_STATUS_INVOICED) { //voor iedereen mogelijk ( && Privilege::hasRight('GLOBAL_ADMIN'))
        $tostatus = Invoice::INVOICE_STATUS_INVOICED;
      }

      return $tostatus;
    }

    /**
     * Makes a copy of the current object
     *
     * @param int $invoice_id (required)
     * @param string[] $reset_addition_properties (optional) resets extra columns
     *
     * @return Invoice
     */
    public static function copy($invoice_id, $reset_addition_properties = []) {

      $invoice = Invoice::getInvoiceAndProductsById($invoice_id);
      $user = User::getUserWithOrganById($invoice->user_id);
      $from_user = User::getUserWithOrganById($invoice->from_user_id);

      $newinvoice = new Invoice($invoice->asArray());
      $newinvoice->id = null;
      $newinvoice->from_db = false;
      $newinvoice->external_id = null;
      $newinvoice->order_id = null;
      $newinvoice->setDatas($user, $from_user);
      $newinvoice->invoice_nr = null;
      $newinvoice->betreft = 'COPY: ' . $newinvoice->getBetreft();
      $newinvoice->status = Invoice::INVOICE_STATUS_NEW;
      $newinvoice->deliverydate = null;
      $newinvoice->invoicedate = null;
      $newinvoice->payeddate = null;
      $newinvoice->reminder1date = null;
      $newinvoice->reminder2date = null;
      $newinvoice->reminder3date = null;
      $newinvoice->insertTS = null;
      $newinvoice->updateTS = null;

      //reset additional properties
      foreach ($reset_addition_properties as $column_name => $column_value) {
        if (isset($newinvoice->{$column_name}) || $newinvoice->{$column_name} === null) {
          $newinvoice->{$column_name} = $column_value;
        }
      }
      $newinvoice->save();

      InvoiceStatus::setStatus($newinvoice);

      $newinvoice->invoice_products = [];
      foreach ($invoice->invoice_products as $ip) {
        $newip = InvoiceProduct::copy($ip, $newinvoice->getId());
        $newip->save();

        $newinvoice->invoice_products[] = $newip;
      }


      if ($invoice->order_id != "") {
        //dit is een bestelling
        if ($newinvoice->shipping != 0) {
          $newip = new InvoiceProduct();
          $newip->invoice_id = $newinvoice->id;
          $newip->description = __("Verzendkosten");
          $newip->size = 1;
          $newip->pieceprice = $newinvoice->shipping;
          $newip->vattype = 21;
          $newip->total = $newinvoice->shipping;
          $newip->sort = 100;
          $newip->save();

          $newinvoice->total_excl_prods += $newinvoice->shipping;
          $newinvoice->total_excl += $newinvoice->shipping;
          $newinvoice->shipping = null;
          $newinvoice->shipping_method = null;
          $newinvoice->shipping_desc = null;
          $newinvoice->shipping_overwrite = null;
          $newinvoice->save();
        }

      }

      return Invoice::getInvoiceAndProductsById($newinvoice->id);
    }

    /**
     * {@inheritdoc}
     */
    public function destroy() {
      InvoiceStatus::delete_by(['invoice_id' => $this->id]);
      InvoiceProduct::delete_by(['invoice_id' => $this->id]);
      if (file_exists(DIR_UPLOADS . 'invoicedata/' . $this->id . '.dat')) {
        unlink(DIR_UPLOADS . 'invoicedata/' . $this->id . '.dat');
      }

      return parent::destroy();
    }

    public function setVoid() {
      $this->ivoid = 1;
      if ($this->invoicedate == "0000-00-00") {
        $this->invoicedate = null;
      }
      $this->save();
    }

    public function calculateTotals() {
      $this->total_excl = 0;
      $this->total_excl_prods = 0;
      $this->vat = 0;
      $this->total = 0;

      if (!isset($this->invoice_products)) {
        $this->invoice_products = InvoiceProduct::getAllByInvoiceId($this->id);
      }

      //calculate total invoice products
      foreach ($this->invoice_products as $ip) {
        $this->total_excl_prods += $ip->getTotal();
        $this->total_excl += $ip->getTotal();
      }

      //calculate others
      $defaultvat = $this->determineVat(); //gebruikt altijd de gedefineerde btw

      if ($this->paymentdiscount != "" && $this->paymentdiscount != null && $this->paymentdiscount != 0) {
        $this->total_excl += $this->paymentdiscount;
      }

      //order costs
      if ($this->handle_costs != "" && $this->handle_costs != null && $this->handle_costs != 0) {
        $this->total_excl += $this->handle_costs;
      }

      //shipping costs
      if ($this->shipping != "" && $this->shipping != 0 && $this->shipping != null) {
        $this->total_excl += $this->shipping;
      }

      if ($this->credits != null && $this->credits != 0) {
        $this->total_excl += -1 * $this->credits;
      }

      //calculate vats
      $vats_arr = $this->getVatSplit(true);
      foreach ($vats_arr as $vkey => $vvalue) {
        $this->vat += $vvalue;
      }

      $this->total = $this->total_excl + $this->vat;
    }

    /**
     * Hashcode for paying invoice online (for instance via mollie), url safe
     *
     * @return string
     */
    public function getHashCode() {
      return EncryptionHelper::encrypt($this->invoice_nr);
    }

    /**
     * Get all unpaid invoices
     *
     * @param bool $in_multivers : filter of deze al in multivers staat
     * @return Invoice[]
     */
    public static function getUnpaidInvoices($in_multivers = true) {
      $filt = 'WHERE NOT invoicedate IS NULL ';
      $filt .= 'AND payeddate IS NULL ';
      if ($in_multivers) {
        $filt .= ' AND NOT external_id IS NULL ';
      }
      $filt .= "ORDER BY invoicedate DESC";

      return Invoice::find_all($filt);
    }

    /**
     * Get all unpaid invoices of organisation
     * @param $organisationId
     * @return Invoice[]
     */
    public static function getUnpaidInvoicesByOrganId(int $organisationId): array {
      $filt = 'AND NOT invoicedate IS NULL ';
      $filt .= 'AND payeddate IS NULL ';
      $filt .= "ORDER BY invoicedate ASC, invoice_nr ASC";
      return Invoice::find_all_by(["organisation_id" => $organisationId], $filt);
    }

    /**
     * Get all open not in multivers/exact invoices
     *
     * @param string $limit bijv "LIMIT 10"
     * @return Invoice[]
     */
    public static function getNotInAccountingInvoices($limit = '') {
      return Invoice::find_all_by(["external_id" => null], "AND NOT invoicedate IS NULL AND YEAR(invoicedate)>=2019 ORDER BY invoicedate ASC " . $limit);
    }

    /**
     * Get all online paid invoices
     *
     * @param string $filter
     * @return Invoice[]
     */
    public static function getOnlinepaidInvoices($filter = '') {
      $filter = ' AND NOT payeddate IS NULL ' . $filter;

      return Invoice::find_all_by(["paymentmethod" => "mollie"], $filter);
    }

    /**
     * Has invoice owner orgnanisation defined a template
     * @param Organisation $organisation
     * @return bool
     */
    public function hasTemplate(Organisation $organisation): bool {
      return $organisation->hasTemplate();
    }

    /**
     * Get invoice sender orgnanisation template path
     * @param Organisation $organisation
     * @return string
     */
    public function getTemplatePath(Organisation $organisation): string {
      return $organisation->getTemplatePath();
    }




  }
